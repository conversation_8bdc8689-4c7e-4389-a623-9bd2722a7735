import React from 'react';
import { ConfigProvider } from 'antd';
import { RouterProvider } from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';
import router from './router';
import StoreProvider from './store/providers';
import { CopilotKit } from '@copilotkit/react-core';
import _api from './constants/api';

import "@copilotkit/react-ui/styles.css";

// App 组件：应用的根组件，设置全局配置和路由
const App: GenieType.FC = React.memo(() => {
  return (
    // <CopilotKit runtimeUrl='https://agent-demo.8btc-ops.com/v2/agui'>
    <CopilotKit runtimeUrl={_api.ag_ui} agent="agno_agent" showDevConsole>
      <StoreProvider>
        <ConfigProvider locale={zhCN}>
          <RouterProvider router={router} />
        </ConfigProvider>
      </StoreProvider>
    </CopilotKit>
  );
});

export default App;
